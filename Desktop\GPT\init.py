import json
import sys
import io
def load_api_config(file_path='api.json'):
    """
    加载指定路径的JSON配置文件（例如 api.json）。

    Args:
        file_path (str): JSON配置文件的路径。默认为 'api.json'。

    Returns:
        dict or None: 如果加载成功，返回包含API配置的字典；
                      如果文件未找到或JSON格式无效，则打印错误信息并返回 None。
    """
    # 使用 try...except 块来处理可能发生的错误（文件找不到、文件内容不是有效的JSON）
    try:
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        # 使用 'with' 语句打开文件。这能确保文件在使用完毕后被正确关闭，即使发生错误。
        # 'r' 表示以只读模式打开文件。
        # 'encoding='utf-8'' 指定使用UTF-8编码读取文件，这是处理JSON文件的常用编码。
        with open(file_path, 'r', encoding='utf-8') as f:
            # 调用 json.load() 函数，它会读取文件对象 f 的内容，
            # 并将其解析为一个Python字典（或其他相应的Python对象）。
            config_data = json.load(f)
            # 如果成功加载，打印一条提示消息（可选，方便调试）
            #print(f"成功从 '{file_path}' 加载 API 配置。")
            # 返回解析得到的Python字典
            return config_data
    # 捕获“文件未找到”的错误
    except FileNotFoundError:
        # 如果文件不存在，打印错误消息
        print(f"错误：配置文件 '{file_path}' 未找到。")
        # 返回 None 表示加载失败
        return None
    # 捕获“JSON解码错误”
    except json.JSONDecodeError:
        # 如果文件内容不是有效的JSON格式，打印错误消息
        print(f"错误：配置文件 '{file_path}' 的格式无效，无法解析为JSON。")
        # 返回 None 表示加载失败
        return None
    # 捕获其他可能的异常（例如权限问题等）
    except Exception as e:
        # 打印通用的错误信息，包括具体的异常类型和描述
        print(f"加载配置文件 '{file_path}' 时发生意外错误: {e}")
        # 返回 None 表示加载失败
        return None

