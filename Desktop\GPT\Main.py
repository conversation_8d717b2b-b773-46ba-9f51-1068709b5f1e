from GPT_image import generate_and_download_image
from Chat import chat
from init import load_api_config
from Novel_Image_Create import generate_novel_images
api = load_api_config()

# 调用函数生成小说配图
output_doc = generate_novel_images(
    novel_path=api["novel"]["url"],
    max_images=api["novel"]["number"],
    character_context="红楼梦的剧情，画风请偏现实一点"
)

if output_doc:
    print(f"成功生成配图文档: {output_doc}")
else:
    print("生成配图文档失败")