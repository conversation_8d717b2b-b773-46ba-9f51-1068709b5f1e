# chat_module.py
import requests
import json
import time
from requests.exceptions import Timeout, ConnectionError, RequestException
from typing import Optional, List, Dict, Any

# --- TavilySearchAPI Class (保持独立，方便复用) ---
class TavilySearchAPI:
    """封装 Tavily 搜索 API 调用的客户端"""
    def __init__(self, api_key: str, request_timeout: int = 30):
        """
        初始化 TavilySearchAPI 客户端。

        Args:
            api_key (str): Tavily API 密钥。
            request_timeout (int, optional): API 请求超时时间（秒）。默认为 30。
        """
        if not api_key:
            raise ValueError("TavilySearchAPI: 必须提供 API 密钥。")
        self.api_key = api_key
        self.api_url = "https://api.tavily.com/search"
        self.request_timeout = request_timeout

    def search(self, query: str, max_results: int = 7, include_answer: bool = True, search_depth: str = "advanced") -> Optional[Dict[str, Any]]:
        """
        执行 Tavily 搜索。

        Args:
            query (str): 搜索查询。
            max_results (int, optional): 返回的最大结果数。默认为 7。
            include_answer (bool, optional): 是否包含 AI 生成的答案。默认为 True。
            search_depth (str, optional): 搜索深度 ('basic' 或 'advanced')。默认为 'advanced'。

        Returns:
            Optional[Dict[str, Any]]: 包含原始 API 响应的字典，如果发生错误则返回 None。
        """
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {self.api_key}"}
        payload = {
            "query": query,
            "search_depth": search_depth,
            "include_answer": include_answer,
            "include_raw_content": False, # 通常我们只需要摘要
            "include_images": False,
            "max_results": max_results
        }
        try:
            # print(f"Debug: 发送 Tavily 搜索请求 (Query: {query[:50]}...) 到 {self.api_url}") # 内部调试信息
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=self.request_timeout)
            response.raise_for_status()
            # print("Debug: Tavily 搜索请求成功。") # 内部调试信息
            return response.json()
        except Timeout:
            print(f"错误: Tavily 搜索请求超时 ({self.request_timeout}秒)")
            return None
        except RequestException as e:
            error_msg = f"错误: Tavily 搜索请求失败: {e} | "
            if hasattr(e, 'response') and e.response is not None:
                error_msg += f"Status: {e.response.status_code}, Body: {e.response.text[:200]}"
            else:
                error_msg += " (无响应)"
            print(error_msg)
            return None
        except json.JSONDecodeError as e:
            resp_text = getattr(response, 'text', '<No Response Text>')
            print(f"错误: 解码 Tavily 响应失败: {e} | 响应: {resp_text[:200]}")
            return None
        except Exception as e:
            print(f"错误: Tavily 搜索中发生未知错误: {e}")
            return None

    @staticmethod
    def format_results(result_json: Optional[Dict[str, Any]], num_results: int = 5, content_length: int = 200) -> Optional[str]:
        """
        将 Tavily 搜索结果的 JSON 格式化为适合 LLM 的字符串。

        Args:
            result_json (Optional[Dict[str, Any]]): Tavily API 返回的 JSON 字典。
            num_results (int, optional): 要格式化的最大结果数量。默认为 5。
            content_length (int, optional): 每个结果内容的最大长度。默认为 200。

        Returns:
            Optional[str]: 格式化后的搜索结果字符串，如果输入无效或无结果则返回 None。
        """
        if not result_json or not isinstance(result_json, dict):
            return None

        context_parts = []
        if result_json.get("answer"):
            context_parts.append(f"搜索摘要: {result_json['answer']}")

        results = result_json.get("results", [])
        if results:
            context_parts.append("\n相关网页:")
            count = 0
            for item in results:
                if count >= num_results:
                    break
                if isinstance(item, dict):
                    title = item.get("title", f"结果 {count + 1}")
                    url = item.get("url", "无链接")
                    content = item.get("content", "无内容") # Tavily 返回的 content 通常已经是摘要
                    formatted_content = content.replace("\n", " ").strip()[:content_length]
                    if formatted_content: # 只添加有内容的条目
                        context_parts.append(f"\n[{count + 1}] {title}\nURL: {url}\n内容: {formatted_content}...")
                        count += 1

        return "\n".join(context_parts).strip() if context_parts else None

# --- 通用 Chat 函数 ---
def chat(
    prompt: str,
    api_key: str,
    api_url: str,
    model_name: str="gpt-4.1-nano",
    enable_search: bool = False,
    search_api_key: Optional[str] = None,
    system_prompt_prefix: Optional[str] = None,
    print_info: bool = False,
    request_timeout: int = 60,
    temperature: float = 0.7,
    max_tokens: int = 3000
) -> Optional[str]:
    """
    通用的聊天函数，支持调用文本模型和可选的联网搜索。

    Args:
        prompt (str): 用户的输入提示词。
        model_name (str): 要调用的文本模型的名称。
        api_key (str): 文本模型 API 的密钥。
        api_url (str): 文本模型 API 的 URL。
        enable_search (bool, optional): 是否启用联网搜索。默认为 False。
        search_api_key (Optional[str], optional): Tavily 搜索 API 密钥，如果 enable_search 为 True 则必须提供。默认为 None。
        system_prompt_prefix (Optional[str], optional): 添加在用户提示词前的固定系统指令字符串。默认为 None。
        print_info (bool, optional): 是否打印详细的调试和过程信息。默认为 False。
        request_timeout (int, optional): API 请求超时时间（秒）。默认为 60。
        temperature (float, optional): LLM 的采样温度。默认为 0.7。
        max_tokens (int, optional): LLM 生成的最大令牌数。默认为 3000。

    Returns:
        Optional[str]: 成功时返回模型的响应文本，失败时返回 None。
    """
    if print_info:
        print("-" * 20)
        print(f"Chat Function Call:")
        print(f"  Prompt: '{prompt[:100]}{'...' if len(prompt)>100 else ''}'")
        print(f"  Model: {model_name} @ {api_url}")
        print(f"  Search Enabled: {enable_search}")
        if enable_search:
            print(f"  Search Key Provided: {'Yes' if search_api_key else 'No'}")
        if system_prompt_prefix:
            print(f"  System Prefix: '{system_prompt_prefix[:50]}...'")

    search_context: Optional[str] = None
    # 1. 执行搜索 (如果启用且有 Key)
    if enable_search:
        if not search_api_key:
            if print_info:
                print("  警告: 启用了搜索但未提供 search_api_key，跳过搜索。")
        else:
            if print_info:
                print("  执行联网搜索...")
            try:
                search_client = TavilySearchAPI(api_key=search_api_key)
                search_start_time = time.time()
                raw_results = search_client.search(prompt)
                search_duration = time.time() - search_start_time
                if raw_results:
                    search_context = TavilySearchAPI.format_results(raw_results)
                    if print_info:
                        if search_context:
                            print(f"  搜索成功 ({search_duration:.2f}s)，获得上下文信息 (长度: {len(search_context)}):")
                            print(f"    '{search_context[:150]}{'...' if len(search_context)>150 else ''}'")
                        else:
                            print(f"  搜索成功 ({search_duration:.2f}s)，但未提取到有效上下文。")
                elif print_info:
                    print(f"  搜索失败或无结果 ({search_duration:.2f}s)。")
            except Exception as search_err:
                if print_info:
                    print(f"  搜索过程中发生错误: {search_err}")
                # 即使搜索失败，也继续进行 LLM 调用 (不带上下文)

    # 2. 准备调用 LLM
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    messages: List[Dict[str, str]] = []

    # 添加搜索结果作为系统信息 (如果存在)
    if search_context:
        messages.append({"role": "system", "content": f"请参考以下实时搜索到的信息来增强你的回答:\n\n{search_context}"})

    # 添加固定的系统设定字符串 (如果存在)
    if system_prompt_prefix:
        messages.append({"role": "system", "content": system_prompt_prefix})

    # 添加用户的主要提示词
    messages.append({"role": "user", "content": prompt})

    payload = {
        "model": model_name,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
        # 可以根据需要添加其他参数，如 top_p 等
    }

    if print_info:
        print(f"  准备调用 LLM ({model_name})...")
        # print(f"  Payload (preview): {json.dumps(payload, indent=2, ensure_ascii=False)[:300]}...") # Optional: 打印部分 payload

    # 3. 调用 LLM API
    try:
        llm_start_time = time.time()
        response = requests.post(api_url, headers=headers, json=payload, timeout=request_timeout)
        response.raise_for_status()  # Raises HTTPError for bad responses (4xx or 5xx)
        llm_duration = time.time() - llm_start_time
        result = response.json()

        # 4. 解析响应
        choices = result.get("choices")
        if isinstance(choices, list) and len(choices) > 0:
            message = choices[0].get("message")
            if isinstance(message, dict):
                content = message.get("content")
                if isinstance(content, str):
                    if print_info:
                        print(f"  LLM 调用成功 ({llm_duration:.2f}s)。")
                        print(f"  Raw Response (preview): '{content[:150]}{'...' if len(content)>150 else ''}'")
                    return content.strip()
                else:
                    if print_info: print(f"  错误: LLM 响应 'content' 格式错误: {type(content)}")
            else:
                if print_info: print(f"  错误: LLM 响应 'message' 格式错误: {message}")
        elif "error" in result:
             error_details = result['error']
             if print_info: print(f"  错误: LLM API 返回错误: {error_details}")
        else:
            if print_info: print(f"  错误: LLM 响应格式错误，缺少 'choices' 或 'error': {result}")

        return None # 表示 LLM 调用失败或格式错误

    except Timeout:
        if print_info: print(f"  错误: LLM 请求超时 ({request_timeout}秒)")
        return None
    except ConnectionError as e:
        if print_info: print(f"  错误: 无法连接到 LLM API ({api_url}): {e}")
        return None
    except RequestException as e:
        error_msg = f"错误: LLM API 请求失败: {e} | "
        if hasattr(e, 'response') and e.response is not None:
            error_msg += f"Status: {e.response.status_code}, Body: {e.response.text[:200]}"
            # 可以在这里尝试解析错误体，如果它是 JSON
            try:
                error_json = e.response.json()
                if print_info: print(f"  API Error Details: {error_json}")
            except json.JSONDecodeError:
                pass # 不是 JSON 错误体
        else:
            error_msg += " (无响应)"
        if print_info: print(f"  {error_msg}")
        return None
    except json.JSONDecodeError:
        resp_text = getattr(response, 'text', '<No Response Text>')
        if print_info: print(f"  错误: 无法解码 LLM JSON 响应: {resp_text[:500]}")
        return None
    except Exception as e:
        if print_info: print(f"  调用 LLM 时发生未知错误: {e}")
        import traceback
        if print_info: traceback.print_exc() # 打印详细堆栈
        return None
    finally:
        if print_info: print("-" * 20)

# --- 这里是主模块的结尾，没有 if __name__ == "__main__": 代码块 ---
# 你可以在下面的 Debug.py 中导入并使用这个模块。
