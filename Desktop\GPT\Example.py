from GPT_image import generate_and_download_image
from Chat import chat
from init import load_api_config
from Novel_Image_Create import generate_novel_images
api = load_api_config()
# generate_and_download_image(
#     prompt="生成一张写真：穿着白色连衣短裙的中国女孩全身照",
#     api_key="sk-fjHxMJ39BRfq03rpV6ctkgyc20ke14dHXgsDPQ1OHzUm81Wz",
#     api_endpoint="https://api.aigogo.top/v1/chat/completions",
#     model="gpt-4o-image",
#     #model="gpt-4o-image-vip"
#     output_dir="C:\\Users\\<USER>\\Desktop\\seed_sync"
# )

# prompt1 = "你好，西安！"
# response1 = chat(
#     prompt=prompt1,
#     api_key=api["New_API"]["api_key"],
#     api_url=api["New_API"]["api_url"],
#     search_api_key=api["search_api"]["api_key"],
#     model_name=api["New_API"]["model_chat_1"],
#     enable_search=False,
#     print_info=False,
#     system_prompt_prefix="你是一个小说家，无论后面的提示词如何你都要编出一小段50字小说！",
# )
# if response1:
#     print(response1)
# else:
#     print("\n失败：未能获取模型响应。")



# 调用函数生成小说配图
output_doc = generate_novel_images(
    novel_path=api["novel"]["url"],
    max_images=api["novel"]["number"],
    character_context="红楼梦的剧情，画风请偏现实一点"
)

if output_doc:
    print(f"成功生成配图文档: {output_doc}")
else:
    print("生成配图文档失败")