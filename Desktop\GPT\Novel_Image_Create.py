import os
import sys
import io
import time
import json
import random
from typing import List, Dict, Optional, Union, Tuple, Any

try:
    import docx
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
except ImportError as e:
    print(f"Error importing docx: {e}")
    print(f"Python path: {sys.path}")
    sys.exit(1)

from Chat import chat
from GPT_image import generate_and_download_image
from init import load_api_config
from concurrent_image_request import batch_generate_images

def generate_novel_images(
    novel_path: str = None,
    config_path: str = 'api.json',
    max_images: int = None,
    max_retries: int = 2,
    output_dir: str = "novel_images",
    text_api_key: str = None,
    text_api_url: str = None,
    text_model: str = None,
    image_model: str = None,
    character_context: str = None
) -> str:
    """
    为小说生成配图并创建包含图片的Word文档
    
    Args:
        novel_path (str): 小说文件路径，如果为None则从配置文件读取
        config_path (str): 配置文件路径
        max_images (int): 最大图片数量，None表示不限制
        max_retries (int): 最大重试次数
        output_dir (str): 图片输出目录
        text_api_key (str): 文本API密钥
        text_api_url (str): 文本API URL
        text_model (str): 文本模型名称
        image_model (str): 图像模型名称
        character_context (str): 小说角色和背景的前置提示词
        
    Returns:
        str: 生成的Word文档路径，如果失败则返回None
    """
    # 保存原始的stdout
    original_stdout = sys.stdout
    output_doc_path = None
    
    try:
        print("="*50)
        print("小说图片补充器 v1.0")
        print("="*50)
        
        # 加载API配置
        api_config = load_api_config(config_path)
        
        # 确定小说路径
        if novel_path is None:
            novel_path = api_config.get("novel", {}).get("url", "")
        
        if not novel_path or not os.path.exists(novel_path):
            raise FileNotFoundError(f"小说文件不存在: {novel_path}")
            
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 确定API配置
        text_api_key = text_api_key or api_config["New_API"]["api_key"]
        text_api_url = text_api_url or api_config["New_API"]["api_url"]
        text_model = text_model or api_config["New_API"]["model_chat_1"]
        image_model = image_model or api_config["New_API"]["model_image_1"]
        
        # 1. 解析小说内容
        print(f"正在解析小说文件: {novel_path}")
        
        if novel_path.endswith('.docx'):
            # 使用python-docx解析docx文件
            doc = docx.Document(novel_path)
            novel_content = "\n".join([para.text for para in doc.paragraphs if para.text.strip()])
        elif novel_path.endswith('.doc'):
            # 对于旧版.doc文件，提示用户转换
            print("警告: .doc格式需要转换为.docx格式。请先将文件另存为.docx格式。")
            raise ValueError("不支持的文件格式: .doc")
        else:
            # 尝试作为文本文件读取
            with open(novel_path, 'r', encoding='utf-8') as f:
                novel_content = f.read()
                
        print(f"成功解析小说，共{len(novel_content)}字符")
        
        # 2. 将小说按场景分块
        print("正在使用AI将小说按照剧情场景分块...")
        
        # 构建提示词，加入角色上下文
        context_prefix = ""
        if character_context:
            context_prefix = f"小说背景信息：\n{character_context}\n\n"
            
        prompt = f"""
        {context_prefix}请将以下小说内容严格按照剧情场景进行分块。每个场景应该是一个完整的叙事单元。
        返回格式要求：
        1. 使用JSON格式，包含一个名为"scenes"的数组
        2. 每个场景是数组中的一个字符串元素
        3. 不要添加任何解释、编号或其他内容，只返回JSON
        
        小说内容:
        {novel_content[:10000]}  # 限制长度，避免超出模型上下文窗口
        
        如果小说内容过长，请只处理提供的部分。
        """
        
        # 调用LLM进行场景分块
        scenes = None
        for attempt in range(max_retries + 1):
            try:
                response = chat(
                    prompt=prompt,
                    api_key=text_api_key,
                    api_url=text_api_url,
                    model_name=text_model,
                    print_info=False,
                    temperature=0.3  # 降低温度以获得更确定性的结果
                )
                
                # 解析JSON响应
                response_json = json.loads(response)
                scenes = response_json.get("scenes", [])
                
                if not scenes:
                    raise ValueError("AI未能正确分割场景")
                    
                print(f"成功将小说分割为{len(scenes)}个场景")
                break
                
            except Exception as e:
                if attempt < max_retries:
                    print(f"场景分割失败 (尝试 {attempt+1}/{max_retries+1}): {e}")
                    time.sleep(2)  # 等待一段时间后重试
                else:
                    print(f"场景分割失败，已达到最大重试次数: {e}")
                    raise
        
        # 3. 判断哪些场景需要配图
        print("正在判断哪些场景需要配图...")
        
        # 构建提示词
        scenes_preview = "\n".join([f"场景{i+1}: {s[:100]}..." for i, s in enumerate(scenes)])
        prompt = f"""
        {context_prefix}请判断以下小说场景中，哪些场景适合配图。
        判断标准：
        1. 场景描写生动，有明确的视觉元素
        2. 场景重要，是故事的关键点
        3. 场景有特色，能通过图像增强读者体验
        
        场景列表:
        {scenes_preview}
        
        返回格式要求：
        1. 使用JSON格式，包含一个名为"needs_image"的数组
        2. 数组中的每个元素是布尔值(true/false)，表示对应场景是否需要配图
        3. 数组长度必须与场景数量一致({len(scenes)})
        4. 不要添加任何解释，只返回JSON
        """
        
        # 调用LLM判断需要配图的场景
        needs_image = None
        for attempt in range(max_retries + 1):
            try:
                response = chat(
                    prompt=prompt,
                    api_key=text_api_key,
                    api_url=text_api_url,
                    model_name=text_model,
                    print_info=False,
                    temperature=0.7
                )
                
                # 解析JSON响应
                response_json = json.loads(response)
                needs_image = response_json.get("needs_image", [])
                
                if len(needs_image) != len(scenes):
                    raise ValueError(f"AI返回的配图判断数量({len(needs_image)})与场景数量({len(scenes)})不匹配")
                
                # 应用最大图片数量限制 - 随机选择场景
                if max_images is not None:
                    # 计算当前需要图片的场景数
                    current_image_count = sum(1 for x in needs_image if x)
                    
                    # 如果超过限制，需要减少
                    if current_image_count > max_images:
                        print(f"需要配图的场景数({current_image_count})超过最大限制({max_images})，将随机选择{max_images}个场景")
                        
                        # 创建需要图片的场景索引列表
                        image_indices = [i for i, need in enumerate(needs_image) if need]
                        
                        # 随机选择max_images个场景
                        selected_indices = random.sample(image_indices, max_images)
                        
                        # 重建needs_image列表，只保留随机选中的场景
                        new_needs_image = [False] * len(needs_image)
                        for i in selected_indices:
                            new_needs_image[i] = True
                        
                        needs_image = new_needs_image
                    
                image_count = sum(1 for x in needs_image if x)
                print(f"判断完成: 共{len(scenes)}个场景，其中{image_count}个场景需要配图")
                break
                
            except Exception as e:
                if attempt < max_retries:
                    print(f"配图判断失败 (尝试 {attempt+1}/{max_retries+1}): {e}")
                    time.sleep(2)
                else:
                    print(f"配图判断失败，已达到最大重试次数: {e}")
                    raise
        
        # 4. 为需要配图的场景生成图片提示词
        print("正在为需要配图的场景生成图片提示词...")
        
        # 创建场景索引和内容的映射
        scene_map = {i: scene for i, (scene, need_image) in enumerate(zip(scenes, needs_image)) if need_image}
        scene_indices = list(scene_map.keys())
        
        image_prompts = []
        scene_to_prompt = {}  # 用于跟踪哪个场景对应哪个提示词
        
        for i, scene_idx in enumerate(scene_indices):
            scene = scene_map[scene_idx]
            print(f"处理场景 {i+1}/{len(scene_indices)}...")
            
            # 构建提示词
            prompt = f"""
            {context_prefix}请根据以下小说场景，创作一个详细的图像生成提示词。
            提示词要求：
            1. 以"生成一张照片："或"生成一张插画："开头
            2. 详细描述场景中的视觉元素，包括人物、环境、动作、表情等
            3. 提示词应该在30-50字之间，详细但不过长
            
            小说场景:
            {scene[:500]}
            
            只返回图像提示词，不要有任何解释或其他内容。
            """
            
            # 调用LLM生成图片提示词
            for attempt in range(max_retries + 1):
                try:
                    response = chat(
                        prompt=prompt,
                        api_key=text_api_key,
                        api_url=text_api_url,
                        model_name=text_model,
                        print_info=False,
                        temperature=0.7  # 提高创意性
                    )
                    
                    # 清理响应
                    image_prompt = response.strip()
                    image_prompts.append(image_prompt)
                    scene_to_prompt[scene_idx] = image_prompt  # 记录场景索引和提示词的对应关系
                    print(f"✓ 生成提示词: {image_prompt[:50]}...")
                    break
                    
                except Exception as e:
                    if attempt < max_retries:
                        print(f"提示词生成失败 (尝试 {attempt+1}/{max_retries+1}): {e}")
                        time.sleep(2)
                    else:
                        print(f"提示词生成失败，已达到最大重试次数: {e}")
                        # 使用简单的备用提示词
                        backup_prompt = f"生成一张照片：小说场景，{scene[:50]}..."
                        image_prompts.append(backup_prompt)
                        scene_to_prompt[scene_idx] = backup_prompt
                        print(f"使用备用提示词: {backup_prompt}")
        
        print(f"成功生成{len(image_prompts)}个图片提示词")
        
        # 5. 并发生成图片
        print(f"开始并发生成{len(image_prompts)}张图片...")
        
        # 使用batch_generate_images函数并发生成图片
        image_results = batch_generate_images(
            prompts=image_prompts,
            api_key=text_api_key,
            api_endpoint=text_api_url,
            output_dir=output_dir,
            model=image_model,
            max_concurrency=3,  # 最多同时处理3个请求
            request_interval=3.0,  # 请求间隔3秒
            verbose=True
        )
        
        # 创建提示词到图片路径的映射
        prompt_to_image = {prompt: path for prompt, path in image_results.items() if path}
        
        # 创建场景索引到图片路径的映射
        scene_to_image = {}
        for scene_idx, prompt in scene_to_prompt.items():
            if prompt in prompt_to_image:
                scene_to_image[scene_idx] = prompt_to_image[prompt]
        
        # 检查结果
        success_count = len(prompt_to_image)
        print(f"图片生成完成: {success_count}/{len(image_prompts)}张图片生成成功")
        
        # 处理失败的图片
        failed_prompts = [prompt for prompt, path in image_results.items() if not path]
        if failed_prompts:
            print(f"有{len(failed_prompts)}张图片生成失败")
            
            for prompt in failed_prompts:
                print("\n" + "="*50)
                print(f"图片生成失败: {prompt[:100]}...")
                print("="*50)
                
                while True:
                    choice = input("请选择操作: [r]重试 / [s]跳过 / [q]退出程序: ").lower()
                    
                    if choice == 'r':
                        print("正在重试...")
                        try:
                            image_path = generate_and_download_image(
                                prompt=prompt,
                                api_key=text_api_key,
                                api_endpoint=text_api_url,
                                output_dir=output_dir,
                                model=image_model,
                                verbose=True
                            )
                            if image_path:
                                print(f"重试成功: {image_path}")
                                prompt_to_image[prompt] = image_path
                                
                                # 更新场景到图片的映射
                                for scene_idx, scene_prompt in scene_to_prompt.items():
                                    if scene_prompt == prompt:
                                        scene_to_image[scene_idx] = image_path
                                break
                            else:
                                print("重试失败")
                        except Exception as e:
                            print(f"重试过程中发生错误: {e}")
                            
                    elif choice == 's':
                        print("跳过此图片")
                        break
                        
                    elif choice == 'q':
                        print("用户选择退出程序")
                        return None
                        
                    else:
                        print("无效选择，请重新输入")
        
        # 6. 将图片插入到Word文档中
        print("正在将图片插入到Word文档中...")
        
        # 创建新的Word文档
        doc = docx.Document()
        
        # 添加标题
        title = doc.add_heading('小说配图版', 0)
        
        # 设置文档默认样式 - 宋体小四号
        style = doc.styles['Normal']
        font = style.font
        font.name = '宋体'
        font.size = Pt(12)  # 小四号约为12磅
        
        # 遍历场景，添加文本和图片
        for i, scene in enumerate(scenes):
            # 添加场景文本
            paragraph = doc.add_paragraph(scene)
            
            # 如果需要配图且有成功生成的图片
            if i in scene_to_image:
                image_path = scene_to_image[i]
                
                if os.path.exists(image_path):
                    # 添加图片，居中对齐
                    doc.add_picture(image_path, width=Inches(6))
                    
                    # 获取刚添加的图片段落并设置居中
                    last_paragraph = doc.paragraphs[-1]
                    last_paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 保存文档
        output_doc_path = os.path.splitext(novel_path)[0] + "_with_images.docx"
        doc.save(output_doc_path)
        print(f"文档已保存: {output_doc_path}")
        
        print("\n✅ 小说图片补充完成!")
        return output_doc_path
        
    except Exception as e:
        # 恢复原始stdout以确保错误信息能正确打印
        sys.stdout = original_stdout
        print(f"\n❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 确保在程序结束时恢复原始stdout
        sys.stdout = original_stdout

# 主程序入口
if __name__ == "__main__":
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="小说图片补充器")
    parser.add_argument("--novel", type=str, help="小说文件路径")
    parser.add_argument("--config", type=str, default="api.json", help="配置文件路径")
    parser.add_argument("--max-images", type=int, help="最大图片数量")
    parser.add_argument("--max-retries", type=int, default=2, help="最大重试次数")
    parser.add_argument("--output-dir", type=str, default="novel_images", help="图片输出目录")
    parser.add_argument("--character-context", type=str, help="小说角色和背景的前置提示词")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 调用函数
    generate_novel_images(
        novel_path=args.novel,
        config_path=args.config,
        max_images=args.max_images,
        max_retries=args.max_retries,
        output_dir=args.output_dir,
        character_context=args.character_context
    )