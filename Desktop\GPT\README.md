小说图片补充器
 1 api文档
1 Chat.py:提供chat 接口
def chat(
    prompt: str,
    api_key: str,
    api_url: str,
    model_name: str="gpt-4.1-nano",
    enable_search: bool = False,
    search_api_key: Optional[str] = None,
    system_prompt_prefix: Optional[str] = None,
    print_info: bool = False,
    request_timeout: int = 60,
    temperature: float = 0.7,
    max_tokens: int = 3000
)
2 GPT_image:提供 generate_and_download_image接口(chat调用)
def generate_and_download_image(
    prompt: str,
    api_key: str,
    api_endpoint: str,
    output_dir: Optional[str] = None,
    model: str = "gpt-4o-image",
    verbose: bool = False
)

3 init.py:提供load_api_config 接口(加载 api.json文件)
def load_api_config(file_path='api.json')

4 Main.py：主程序，运行即可

5 api.json:包含搜索api、api提供商和其相关信息


工作流1：
初始化读入小说的word地址，解析小说内容
LLM1：严格对文本按照剧情场景分块，传入准备好的list，在word中留好插入图片的索引，等待可能的图片插入
LLM2：依次判断是否需要每一块是否需要配图，返回一个bool数组，与前面对应
对于需要需要插入的块，调用LLM3，生成提示词，存入list
调用LLM4(生图模型)，并发生成图片，保存并按照之前留好的word插入索引，将图片插入
(语言模型调用错误则每个重试一遍，如果仍然出错则停止程序)
(需要注意的是，如果图片请求出现错误，应该打印错误信息，并暂停下来，让用户决定跳过还是重试)
