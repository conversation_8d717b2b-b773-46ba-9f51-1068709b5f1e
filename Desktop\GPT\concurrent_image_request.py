import os
import time
import concurrent.futures
from typing import List, Dict, Optional, Union, Tuple
from GPT_image import generate_and_download_image
from Chat import chat

def translate_prompt(
    prompt: str,
    api_key: str,
    api_endpoint: str,
    model: str,
    verbose: bool = False
) -> str:
    """
    使用Chat接口处理图像提示词，优化其表达和内容。
    
    Args:
        prompt (str): 原始提示词
        api_key (str): API密钥
        api_endpoint (str): API端点
        model (str): 使用的文本模型
        verbose (bool): 是否显示详细信息
        
    Returns:
        str: 处理后的提示词
    """
    if verbose:
        print(f"处理提示词: '{prompt[:50]}...'")
    
    system_prompt = """
    你是一个专业的图像提示词优化专家。你的任务是将用户输入的中文提示词翻译成英文，并进行优化，使其能生成更好的图像。
    
    请遵循以下规则：
    1. 保留原始提示词的核心内容和意图
    2. 使用更精确、更具描述性的英文词汇
    3. 添加适当的艺术风格、光影效果等细节
    4. 确保翻译后的提示词长度适中（50-100个单词）
    5. 直接返回优化后的英文提示词，不要有任何解释或其他内容
    """
    
    try:
        translated = chat(
            prompt=prompt,
            api_key=api_key,
            api_url=api_endpoint,
            model_name=model,
            system_prompt_prefix=system_prompt,
            print_info=False,
            temperature=0.7
        )
        
        if verbose:
            print(f"处理结果: '{translated[:50]}...'")
        
        return translated.strip()
    except Exception as e:
        if verbose:
            print(f"提示词处理失败: {e}")
        # 处理失败时返回原始提示词
        return prompt

def batch_generate_images(
    prompts: List[str],
    api_key: str,
    api_endpoint: str,
    output_dir: Optional[str] = None,
    model: str = "gpt-4o-image",
    max_concurrency: int = 3,
    request_interval: float = 1.0,
    verbose: bool = False,
    translated: bool = False,
    text_model: str = "gpt-4.1-nano"
) -> Dict[str, Union[str, None]]:
    """
    Generates multiple images concurrently with controlled parallelism and request intervals.

    Args:
        prompts (List[str]): List of text prompts for image generation.
        api_key (str): Your API Key for authentication.
        api_endpoint (str): The API endpoint URL for chat completions.
        output_dir (Optional[str]): Directory to save images. Defaults to user's Desktop.
        model (str): Model name for image generation. Defaults to "gpt-4o-image".
        max_concurrency (int): Maximum number of concurrent requests. Defaults to 3.
        request_interval (float): Minimum interval between requests in seconds. Defaults to 1.0.
        verbose (bool): Whether to print detailed status messages. Defaults to False.
        translated (bool): Whether to translate prompts to English before generating images. Defaults to False.
        text_model (str): Model name for text translation. Defaults to "gpt-4.1-nano".

    Returns:
        Dict[str, Union[str, None]]: Dictionary mapping prompts to their saved image paths or None if failed.
    """
    results = {}
    original_to_translated = {}  # 用于跟踪原始提示词到翻译后提示词的映射
    
    if verbose:
        print(f"Starting batch image generation with {len(prompts)} prompts")
        print(f"Max concurrency: {max_concurrency}, Request interval: {request_interval}s")
        if translated:
            print(f"Prompt translation enabled using model: {text_model}")
    
    # Create output directory if specified and doesn't exist
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        if verbose:
            print(f"Created output directory: {output_dir}")
    
    # 如果需要翻译，先处理所有提示词
    processed_prompts = prompts
    if translated:
        if verbose:
            print("Translating and optimizing prompts...")
        
        processed_prompts = []
        for i, prompt in enumerate(prompts):
            if verbose:
                print(f"[{i+1}/{len(prompts)}] Processing prompt...")
            
            translated_prompt = translate_prompt(
                prompt=prompt,
                api_key=api_key,
                api_endpoint=api_endpoint,
                model=text_model,
                verbose=verbose
            )
            
            processed_prompts.append(translated_prompt)
            original_to_translated[prompt] = translated_prompt
            
            if verbose:
                print(f"Original: '{prompt[:50]}...'")
                print(f"Processed: '{translated_prompt[:50]}...'")
                print("-" * 40)
    
    # Function to process a single prompt with rate limiting
    def process_prompt(prompt_data: Tuple[int, str, str]) -> Tuple[str, str, Optional[str]]:
        idx, original_prompt, processed_prompt = prompt_data
        
        # Apply rate limiting through simple delay
        if idx > 0:  # No delay for the first request
            time.sleep(request_interval)
            
        if verbose:
            print(f"[{idx+1}/{len(processed_prompts)}] Generating image for: '{processed_prompt[:50]}...'")
            
        result = generate_and_download_image(
            prompt=processed_prompt,
            api_key=api_key,
            api_endpoint=api_endpoint,
            output_dir=output_dir,
            model=model,
            verbose=verbose
        )
        
        return (original_prompt, processed_prompt, result)
    
    # Use ThreadPoolExecutor for concurrent processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrency) as executor:
        # Submit all tasks and collect futures
        future_to_prompt = {}
        
        for i, processed_prompt in enumerate(processed_prompts):
            # 找到对应的原始提示词
            original_prompt = prompts[i] if not translated else [k for k, v in original_to_translated.items() if v == processed_prompt][0]
            
            future = executor.submit(process_prompt, (i, original_prompt, processed_prompt))
            future_to_prompt[future] = original_prompt
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_prompt):
            original_prompt, processed_prompt, image_path = future.result()
            results[original_prompt] = image_path
            
            if verbose:
                status = "✅ Success" if image_path else "❌ Failed"
                if translated:
                    print(f"{status} - Original: '{original_prompt[:30]}...'")
                    print(f"  Processed: '{processed_prompt[:30]}...'")
                else:
                    print(f"{status} - Prompt: '{original_prompt[:50]}...'")
                
                if image_path:
                    print(f"  Saved to: {image_path}")
    
    # Summary
    if verbose:
        success_count = sum(1 for path in results.values() if path)
        print(f"\nBatch processing complete: {success_count}/{len(prompts)} images generated successfully")
    
    return results

# Example usage
if __name__ == "__main__":
    from init import load_api_config
    
    # Load API configuration
    api_config = load_api_config()
    
    # Example prompts
    test_prompts = [
        "全身特写，美丽的中国JK少女，比例9：16",
        "全身特写，美丽的JK中国白色连衣裙露肩少女，比例9：16",
        "全身特写，美丽的中国JK少女，比例9：16",
        "全身特写，美丽的JK中国JK露肩少女，比例9：16",
    ]
    
    # Generate images in batch with translation
    results = batch_generate_images(
        prompts=test_prompts,
        api_key=api_config["New_API"]["api_key"],
        api_endpoint=api_config["New_API"]["api_url"],
        output_dir="generated_images",
        model=api_config["New_API"]["model_image_1"],
        max_concurrency=4,
        request_interval=3.0,
        verbose=False,
        translated=True,  # 启用提示词翻译
        text_model=api_config["New_API"]["model_chat_1"]
    )
    
    # Print results
    print("\nResults summary:")
    for prompt, path in results.items():
        status = "Success" if path else "Failed"
        print(f"- {status}: {prompt[:50]}...")
